"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { GradientBorder } from "@/components/ui/gradient-border";
import { Link } from '@/i18n/navigation';
import Image from "next/image";
import { CheckCircle2, <PERSON>, <PERSON><PERSON>, <PERSON>, Zap } from "lucide-react";
import { motion } from "framer-motion";
import { PerformanceMonitor } from "@/components/ui/performance-monitor";
import { useTranslations } from 'next-intl';

export default function TestimonialsPage() {
  const t = useTranslations('TestimonialsPage');

  return (
    <>
      {/* Preload critical resources */}
      <link rel="preload" href="/testimonials/C_tutor_web.png" as="image" />

      <div className="min-h-screen overflow-x-hidden bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 text-white">
      {/* Hero Section - Enhanced */}
      <div className="pt-32 pb-30 px-4 sm:px-6 lg:px-8 text-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-purple-950/80 to-transparent z-0"></div>
        {/* Decorative gradient blobs - Reduced for performance */}
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-pink-500/15 rounded-full filter blur-3xl opacity-40 animate-blob"></div>
        <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-purple-500/15 rounded-full filter blur-3xl opacity-40 animate-blob animation-delay-4000"></div>
        
        <div className="relative z-10">
          <h1 className="text-4xl lg:text-5xl font-bold pb-1 bg-clip-text text-transparent bg-gradient-to-r from-pink-300 to-white">
            {t('heroTitle')}
          </h1>
      
        </div>
      </div>

      {/* Optimized animation styles */}
      <style jsx global>{`
        @keyframes blob {
          0%, 100% {
            transform: scale(1) translate(0px, 0px);
          }
          50% {
            transform: scale(1.05) translate(15px, -25px);
          }
        }
        .animate-blob {
          animation: blob 12s ease-in-out infinite;
          will-change: transform;
        }
        .animation-delay-4000 {
          animation-delay: 6s;
        }
      `}</style>

      {/* Featured Project Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Project showcase card - Removed scroll animation for better performance */}
          <motion.div
            initial={{ opacity: 1, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <Card className="w-full bg-purple-900/30 backdrop-blur-sm border border-purple-700/30 overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:border-purple-500/50">
            {/* Top ribbon */}
            <div className="bg-gradient-to-r from-pink-600 to-purple-600 py-3 px-6">
              <p className="text-sm font-medium text-white/90 flex items-center">
                <span className="mr-2">✨</span> {t('featuredProject')}
              </p>
            </div>
            
            <div className="md:flex">
              {/* Left column - Mockup */}
              <div className="md:w-1/2 p-6 md:p-8 flex flex-col items-center justify-center gap-4">
                {/* Browser Mockup */}
                <div className="w-full max-w-xl rounded-xl overflow-hidden shadow-2xl bg-gray-800 border border-gray-600">
                  {/* Browser Top Bar */}
                  <div className="h-8 bg-gray-700 flex items-center px-3">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  {/* Browser Content - Optimized Image */}
                  <div className="relative w-full h-auto">
                    <Image
                      src="/testimonials/C_tutor_web.png"
                      alt="Alexandre C Website Screenshot"
                      width={800}
                      height={600}
                      priority={true}
                      className="w-full h-auto block hover:scale-[1.02] transition-transform duration-300"
                      placeholder="blur"
                      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                    />
                  </div>
                </div>
                {/* Check it out button */}
                <Link href="https://alexandre-c.netlify.app" target="_blank" rel="noopener noreferrer">
                  <Button 
                    variant="outline" 
                    className="w-full sm:w-auto bg-gradient-to-r from-pink-600 to-purple-600 text-white border-0 hover:from-pink-500 hover:to-purple-500 hover:text-white px-4 py-2 sm:px-6 sm:py-3 text-lg font-medium"
                  >
                    {t('checkItOut')}
                  </Button>
                </Link>
              </div>
              
              {/* Right column - Content */}
              <div className="md:w-3/5 p-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-pink-100">
                  {t('projectTitle')}
                </h2>
                
                {/* Quote section */}
                <div className="my-6 relative">
                  <blockquote className="italic relative">
                    <div className="absolute -left-1 -top-1 text-4xl text-pink-500 opacity-50">"</div>
                    <div className="absolute -right-1 -bottom-1 text-4xl text-pink-500 opacity-50">"</div>
                    <p className="pl-6 pr-6 italic text-lg text-white border-l-2 border-pink-500 transition-all duration-300 hover:border-pink-400 hover:pl-7">
                        {t('testimonial1Quote')}
                    </p>
                    <footer className="mt-2 font-normal text-sm text-purple-200 not-italic">
                      {t('testimonial1Author')}
                    </footer>
                  </blockquote>
                </div>
                
                {/* The Challenge */}
                <div className="mb-6">
                  <h3 className="font-semibold text-xl flex items-center mb-2 text-white">
                    <span className="mr-2 text-purple-400">●</span> {t('challengeTitle')}
                  </h3>
                  <p className="text-white/90">
                    {t('challengeDescription')}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Bottom section */}
            <div className="p-8 pt-0 space-y-12"> {/* Kept space-y-12 */}
              {/* What We Delivered */}
              <div className="bg-gradient-to-br from-purple-900/40 via-purple-800/30 to-pink-900/40 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-purple-700/30"> {/* UpZera gradient styling */}
                <h3 className="font-semibold text-xl flex items-center mb-6 text-white border-b border-purple-400/50 pb-2"> {/* White text with purple border */}
                  <span className="mr-2 text-pink-400">🛠️</span> {t('deliveredTitle')}
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Item 1 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('delivered1Title')}</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">{t('delivered1Description')}</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 2 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('delivered2Title')}</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">{t('delivered2Description')}</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 3 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Brain className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('delivered3Title')}</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">{t('delivered3Description')}</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 4 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Palette className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('delivered4Title')}</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">{t('delivered4Description')}</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                </div>
              </div>

              {/* Tech Stack */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg">
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2">
                  <span className="mr-2 text-purple-600">🖥️</span> {t('techStackTitle')}
                </h3>
                <div className="flex flex-row gap-8 justify-center items-center"> {/* Outer: Row, center horizontally and vertically */}
                  <div className="flex flex-col items-center gap-2"> {/* Inner React: Column, center items horizontally */}
                    <Image
                      src="/logo/tech-stack/react.svg"
                      alt="React Logo"
                      width={40}
                      height={40}
                      className="h-10 w-10"
                      loading="lazy"
                    />
                    <span className="font-medium text-lg text-purple-800">React</span>
                  </div>
                  <div className="flex flex-col items-center gap-2"> {/* Inner Vite: Column, center items horizontally */}
                    <Image
                      src="/logo/vite-icon.svg"
                      alt="Vite Logo"
                      width={40}
                      height={40}
                      className="h-10 w-10"
                      loading="lazy"
                    />
                    <span className="font-medium text-lg text-purple-800">Vite</span>
                  </div>
                </div>
           
              </div>
              
              {/* Results */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg"> {/* Changed background to bg-gray-100 */}
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2"> {/* Adjusted heading color and border */}
                  <span className="mr-2 text-purple-600">💡</span> {t('resultsTitle')}
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Item 1 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('result1')}</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 2 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('result2Title')}</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600">{t('result2Description')}</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 3 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('result3')}</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 4 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{t('result4')}</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                </div>
              </div>
              
             
            </div>
          </Card>
          </motion.div>
        </div>
      </div>
      
      {/* Standalone CTA Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-purple-600 backdrop-blur-sm border-y border-purple-700/30 mt-32">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-pink-200">
            {t('ctaTitle')}
          </h2>
          <p className="mb-8 text-xl text-white/90 leading-relaxed font-light max-w-2xl mx-auto">
          {t('ctaDescription')}
          </p>
          <Link href="/contact">
            <Button 
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:scale-105 hover:shadow-xl text-lg px-4 py-3 sm:px-8 sm:py-6"
            >
              {t('ctaButton')}
            </Button>
          </Link>

        </div>
      </div>

      {/* Gradient border before footer - Optimized */}
      <div className="w-full mt-8">
        <GradientBorder
          width={1200}
          animationDuration={6} // Slower animation for better performance
          strokeWidth={3}
        />
      </div>
      
      {/* Footer is added via layout */}
      </div>

      {/* Performance Monitor - Remove in production */}
      <PerformanceMonitor />
    </>
  );
}
